@extends('components.layouts.admin')

@section('title', __('Site Settings'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Site Settings') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- Social Media Settings Card -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3 d-flex align-items-center justify-content-center rounded-circle bg-primary" style="width: 48px; height: 48px;">
                                    <i class="fas fa-share-alt text-white"></i>
                                </div>
                                <div>
                                    <h5 class="card-title font-weight-bold mb-0">{{ __('Social Media') }}</h5>
                                    <p class="card-text text-muted small mb-0">{{ __('Manage social media links') }}</p>
                                </div>
                            </div>
                            <p class="card-text">Configure your social media links that appear in the website footer.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="social-preview">
                                    @if(App\Models\Setting::get('social_facebook'))
                                        <i class="fab fa-facebook-f text-primary mr-2"></i>
                                    @endif
                                    @if(App\Models\Setting::get('social_twitter'))
                                        <i class="fab fa-twitter text-info mr-2"></i>
                                    @endif
                                    @if(App\Models\Setting::get('social_linkedin'))
                                        <i class="fab fa-linkedin-in text-primary mr-2"></i>
                                    @endif
                                    @if(App\Models\Setting::get('social_instagram'))
                                        <i class="fab fa-instagram text-danger"></i>
                                    @endif
                                </div>
                                <a href="{{ route('admin.settings.social-media') }}" class="btn btn-primary btn-sm">
                                    {{ __('Manage') }}
                                    <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Consultation Status Settings Card -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3 d-flex align-items-center justify-content-center rounded-circle bg-success" style="width: 48px; height: 48px;">
                                    <i class="fas fa-tags text-white"></i>
                                </div>
                                <div>
                                    <h5 class="card-title font-weight-bold mb-0">{{ __('Consultation Statuses') }}</h5>
                                    <p class="card-text text-muted small mb-0">{{ __('Manage consultation status types') }}</p>
                                </div>
                            </div>
                            <p class="card-text">Configure custom status types for tracking consultation progress.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="small font-weight-bold">
                                    <span class="text-muted">{{ App\Models\ConsultationStatus::count() }}</span> status types
                                </div>
                                <a href="{{ route('admin.consultation-statuses.index') }}" class="btn btn-success btn-sm">
                                    {{ __('Manage') }}
                                    <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Settings Card -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3 d-flex align-items-center justify-content-center rounded-circle bg-warning" style="width: 48px; height: 48px;">
                                    <i class="fas fa-key text-white"></i>
                                </div>
                                <div>
                                    <h5 class="card-title font-weight-bold mb-0">{{ __('API Settings') }}</h5>
                                    <p class="card-text text-muted small mb-0">{{ __('Manage API configuration') }}</p>
                                </div>
                            </div>
                            <p class="card-text">Configure API settings including allowed IPs and token expiration.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="small font-weight-bold">
                                    <span class="text-muted">{{ App\Models\Setting::get('allowed_api_ips') ? count(explode(',', App\Models\Setting::get('allowed_api_ips'))) : 0 }}</span> IPs allowed
                                </div>
                                <a href="{{ route('admin.settings.api') }}" class="btn btn-warning btn-sm">
                                    {{ __('Manage') }}
                                    <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Tokens Card -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3 d-flex align-items-center justify-content-center rounded-circle bg-danger" style="width: 48px; height: 48px;">
                                    <i class="fas fa-lock text-white"></i>
                                </div>
                                <div>
                                    <h5 class="card-title font-weight-bold mb-0">{{ __('API Tokens') }}</h5>
                                    <p class="card-text text-muted small mb-0">{{ __('Manage API access tokens') }}</p>
                                </div>
                            </div>
                            <p class="card-text">Create and manage API tokens for secure API access.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="small font-weight-bold">
                                    <span class="text-muted">{{ \Laravel\Sanctum\PersonalAccessToken::count() }}</span> active tokens
                                </div>
                                <a href="{{ route('admin.settings.api-tokens') }}" class="btn btn-danger btn-sm">
                                    {{ __('Manage') }}
                                    <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
